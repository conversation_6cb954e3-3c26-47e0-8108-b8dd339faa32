<?php 

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

global $conexion;

use App\classes\EquipoDiccionario;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/general/preparar.php';
require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/classes/EquipoDiccionario.php';

$newequipodiccionario = new EquipoDiccionario;

#region initialize variables
$success_display = '';
$success_text = '';
$error_display = '';
$error_text = '';
#endregion initialize variables

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_GET['m'])) {
            $success_display = 'show';
            $success_text = 'El registro ha sido modificado.';
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get

#region sub_add
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
    try {
        // Use setters
        $newequipodiccionario->setPlataforma(limpiar_datos($_POST['plataforma']));
        $newequipodiccionario->setIdTeam((int)limpiar_datos($_POST['id_team']));
        $newequipodiccionario->setNombreEnPlataforma(limpiar_datos($_POST['nombre_en_plataforma']));

        // Use guardar method
        $newequipodiccionario->guardar($conexion);

        $success_display = 'show';
        $success_text = 'El registro ha sido ingresado.';

        $newequipodiccionario = new EquipoDiccionario();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_add

#region sub_delequipodiccionario
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delequipodiccionario'])) {
    try {
        $delidequipodiccionario = limpiar_datos($_POST['mdl_delequipodiccionario_idequipodiccionario']);

        EquipoDiccionario::delete($delidequipodiccionario, $conexion);

        $success_display = 'show';
        $success_text = 'El registro ha sido eliminado.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_delequipodiccionario

#region try
try {
    // Get all EquipoDiccionario records
    $equiposdiccionario = EquipoDiccionario::getList($conexion);

    // Get unique team names from active matches for autocomplete
    $query = "SELECT DISTINCT home as team_name, id_home as team_id FROM partidos WHERE estado = 1
              UNION
              SELECT DISTINCT away as team_name, id_away as team_id FROM partidos WHERE estado = 1
              ORDER BY team_name";
    $statement = $conexion->prepare($query);
    $statement->execute();
    $teams_for_autocomplete = $statement->fetchAll(PDO::FETCH_ASSOC);

    // Create a mapping of team IDs to team names for display in the listing
    $team_names_map = [];
    foreach ($teams_for_autocomplete as $team) {
        $team_names_map[$team['team_id']] = $team['team_name'];
    }

    // Initialize arrays if they don't exist
    if (!isset($equiposdiccionario)) {
        $equiposdiccionario = [];
    }
    if (!isset($teams_for_autocomplete)) {
        $teams_for_autocomplete = [];
    }
    if (!isset($team_names_map)) {
        $team_names_map = [];
    }

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();

    // Initialize arrays even on error to prevent undefined variable errors
    $equiposdiccionario = [];
    $teams_for_autocomplete = [];
    $team_names_map = [];
}
#endregion try

require_once __ROOT__ . '/views/lequipos_diccionario.view.php';

?>
