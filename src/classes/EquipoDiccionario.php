<?php

declare(strict_types=1);

namespace App\classes;

use InvalidArgumentException;
use Exception;
use PDO;
use PDOException;

/**
 * Represents a team dictionary entry (EquipoDiccionario).
 * Maps team names across different platforms.
 */
class EquipoDiccionario
{
    // --- Attributes ---
    private ?int $id = null;
    private ?string $plataforma = null;
    private ?int $id_team = null;
    private ?string $nombre_en_plataforma = null;

    /**
     * Constructor: Initializes properties with default null values.
     */
    public function __construct()
    {
        $this->id = null;
        $this->plataforma = null;
        $this->id_team = null;
        $this->nombre_en_plataforma = null;
    }

    /**
     * Static factory method to create an instance from an array (e.g., DB result).
     *
     * @param array $data Associative array containing property values.
     * @return self Returns a new instance of EquipoDiccionario.
     * @throws Exception If data is invalid.
     */
    public static function construct(array $data): self
    {
        try {
            $objeto = new self();
            $objeto->id = isset($data['id']) ? (int)$data['id'] : null;
            $objeto->plataforma = $data['plataforma'] ?? null;
            $objeto->id_team = isset($data['id_team']) ? (int)$data['id_team'] : null;
            $objeto->nombre_en_plataforma = $data['nombre_en_plataforma'] ?? null;

            return $objeto;

        } catch (Exception $e) {
            error_log("Error constructing EquipoDiccionario from data: " . print_r($data, true) . " Error: " . $e->getMessage());
            throw new Exception("Error constructing EquipoDiccionario: " . $e->getMessage());
        }
    }

    /**
     * Retrieves an EquipoDiccionario object from the database by its ID.
     *
     * @param int $id The ID of the EquipoDiccionario to retrieve.
     * @param PDO $conexion The database connection object.
     * @return self|null An EquipoDiccionario object if found, null otherwise.
     * @throws Exception If DB error occurs.
     * @throws InvalidArgumentException If the provided ID is invalid.
     */
    public static function get(int $id, PDO $conexion): ?self
    {
        if ($id <= 0) {
            throw new InvalidArgumentException("Invalid ID provided.");
        }

        try {
            $query = <<<SQL
            SELECT *
            FROM equipos_diccionario
            WHERE id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? static::construct($resultado) : null;

        } catch (PDOException $e) {
            error_log("Database error getting EquipoDiccionario (ID: $id): " . $e->getMessage());
            throw new Exception("Database error fetching EquipoDiccionario: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("Error getting EquipoDiccionario (ID: $id): " . $e->getMessage());
            throw new Exception("Error fetching EquipoDiccionario: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a list of all EquipoDiccionario objects.
     *
     * @param PDO $conexion The database connection object.
     * @return array An array of EquipoDiccionario objects. Returns an empty array if no results.
     * @throws Exception If there is an error during the database query or object construction.
     */
    public static function getList(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT *
            FROM equipos_diccionario
            ORDER BY plataforma, nombre_en_plataforma
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            if ($resultados) {
                foreach ($resultados as $resultado) {
                    try {
                        $listado[] = static::construct($resultado);
                    } catch (Exception $e) {
                        error_log("Error constructing EquipoDiccionario during getList for data: " . print_r($resultado, true) . " Error: " . $e->getMessage());
                        // Skip this item and continue
                    }
                }
            }
            return $listado;

        } catch (PDOException $e) {
            error_log("Database error getting EquipoDiccionario list: " . $e->getMessage());
            throw new Exception("Database error fetching EquipoDiccionario list: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("Error getting EquipoDiccionario list: " . $e->getMessage());
            throw new Exception("Error fetching EquipoDiccionario list: " . $e->getMessage());
        }
    }

    /**
     * Retrieves EquipoDiccionario objects by platform.
     *
     * @param string $plataforma The platform to filter by.
     * @param PDO $conexion The database connection object.
     * @return array An array of EquipoDiccionario objects for the specified platform.
     * @throws Exception If there is an error during the database query.
     */
    public static function getByPlataforma(string $plataforma, PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT *
            FROM equipos_diccionario
            WHERE plataforma = :plataforma
            ORDER BY nombre_en_plataforma
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":plataforma", $plataforma, PDO::PARAM_STR);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            if ($resultados) {
                foreach ($resultados as $resultado) {
                    try {
                        $listado[] = static::construct($resultado);
                    } catch (Exception $e) {
                        error_log("Error constructing EquipoDiccionario during getByPlataforma for data: " . print_r($resultado, true) . " Error: " . $e->getMessage());
                        // Skip this item and continue
                    }
                }
            }
            return $listado;

        } catch (PDOException $e) {
            error_log("Database error getting EquipoDiccionario by platform ($plataforma): " . $e->getMessage());
            throw new Exception("Database error fetching EquipoDiccionario by platform: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("Error getting EquipoDiccionario by platform ($plataforma): " . $e->getMessage());
            throw new Exception("Error fetching EquipoDiccionario by platform: " . $e->getMessage());
        }
    }

    /**
     * Retrieves EquipoDiccionario objects by team ID.
     *
     * @param int $idTeam The team ID to filter by.
     * @param PDO $conexion The database connection object.
     * @return array An array of EquipoDiccionario objects for the specified team.
     * @throws Exception If there is an error during the database query.
     */
    public static function getByTeam(int $idTeam, PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT *
            FROM equipos_diccionario
            WHERE id_team = :id_team
            ORDER BY plataforma
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id_team", $idTeam, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            if ($resultados) {
                foreach ($resultados as $resultado) {
                    try {
                        $listado[] = static::construct($resultado);
                    } catch (Exception $e) {
                        error_log("Error constructing EquipoDiccionario during getByTeam for data: " . print_r($resultado, true) . " Error: " . $e->getMessage());
                        // Skip this item and continue
                    }
                }
            }
            return $listado;

        } catch (PDOException $e) {
            error_log("Database error getting EquipoDiccionario by team ($idTeam): " . $e->getMessage());
            throw new Exception("Database error fetching EquipoDiccionario by team: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("Error getting EquipoDiccionario by team ($idTeam): " . $e->getMessage());
            throw new Exception("Error fetching EquipoDiccionario by team: " . $e->getMessage());
        }
    }

    /**
     * Saves (inserts or updates) the current EquipoDiccionario instance to the database.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If validation fails or a database error occurs.
     */
    public function guardar(PDO $conexion): bool
    {
        // Determine if it's an insert or update
        $isInsertOperation = ($this->getId() === null);

        // Call validation
        $this->validarDatos();

        try {
            if (!$isInsertOperation) {
                return $this->_update($conexion);
            } else {
                return $this->_insert($conexion);
            }
        } catch (PDOException $e) {
            $idInfo = $this->getId() ?? 'N/A';
            error_log("Database error saving EquipoDiccionario (ID: {$idInfo}): " . $e->getMessage());
            throw new Exception("Database error saving EquipoDiccionario: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("General error saving EquipoDiccionario: " . $e->getMessage());
            throw new Exception("Error saving EquipoDiccionario: " . $e->getMessage());
        }
    }

    /**
     * Inserts the current EquipoDiccionario instance into the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     */
    private function _insert(PDO $conexion): bool
    {
        $query = <<<SQL
        INSERT INTO equipos_diccionario (
            plataforma,
            id_team,
            nombre_en_plataforma
        ) VALUES (
            :plataforma,
            :id_team,
            :nombre_en_plataforma
        )
        SQL;

        $statement = $conexion->prepare($query);

        $statement->bindValue(':plataforma', $this->getPlataforma(), PDO::PARAM_STR);
        $statement->bindValue(':id_team', $this->getIdTeam(), PDO::PARAM_INT);
        $statement->bindValue(':nombre_en_plataforma', $this->getNombreEnPlataforma(), PDO::PARAM_STR);

        $success = $statement->execute();

        if ($success) {
            // Get the last inserted ID
            $lastId = $conexion->lastInsertId();
            if ($lastId) {
                $this->setId((int)$lastId);
            } else {
                error_log("Failed to retrieve lastInsertId after EquipoDiccionario insert.");
                return false;
            }
        } else {
            error_log("Failed to insert EquipoDiccionario: " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Updates the current EquipoDiccionario instance in the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If ID is invalid.
     */
    private function _update(PDO $conexion): bool
    {
        if ($this->getId() === null || $this->getId() <= 0) {
            throw new Exception("Cannot update EquipoDiccionario without a valid ID.");
        }

        $query = <<<SQL
        UPDATE equipos_diccionario SET
             plataforma = :plataforma
            ,id_team = :id_team
            ,nombre_en_plataforma = :nombre_en_plataforma
        WHERE id = :id
        SQL;

        $statement = $conexion->prepare($query);

        $statement->bindValue(':plataforma', $this->getPlataforma(), PDO::PARAM_STR);
        $statement->bindValue(':id_team', $this->getIdTeam(), PDO::PARAM_INT);
        $statement->bindValue(':nombre_en_plataforma', $this->getNombreEnPlataforma(), PDO::PARAM_STR);
        $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

        $success = $statement->execute();

        if (!$success) {
            error_log("Failed to update EquipoDiccionario (ID: {$this->getId()}): " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Deletes an EquipoDiccionario record from the database.
     *
     * @param int $id The ID of the EquipoDiccionario to delete.
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If DB error occurs.
     * @throws InvalidArgumentException If the ID is invalid.
     */
    public static function delete(int $id, PDO $conexion): bool
    {
        if ($id <= 0) {
            throw new InvalidArgumentException("Invalid ID provided for deletion.");
        }

        try {
            $query = <<<SQL
            DELETE FROM equipos_diccionario
            WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            error_log("Database error deleting EquipoDiccionario (ID: $id): " . $e->getMessage());
            throw new Exception("Database error deleting EquipoDiccionario: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("Error deleting EquipoDiccionario (ID: $id): " . $e->getMessage());
            throw new Exception("Error deleting EquipoDiccionario: " . $e->getMessage());
        }
    }

    /**
     * Validates the essential data for the EquipoDiccionario.
     * Throws an Exception if validation fails.
     *
     * @throws Exception If validation fails.
     */
    private function validarDatos(): void
    {
        // Assume global functions exist and handle throwing exceptions on failure
        if (!function_exists('validar_textovacio')) {
            throw new Exception("Required global validation function 'validar_textovacio' is missing.");
        }

        try {
            validar_textovacio($this->getPlataforma(), 'Debe especificar la plataforma');
            validar_textovacio($this->getNombreEnPlataforma(), 'Debe especificar el nombre en plataforma');

            // Validate id_team
            if ($this->getIdTeam() === null || $this->getIdTeam() <= 0) {
                throw new Exception('Debe especificar un ID de equipo válido');
            }

        } catch (Exception $e) {
            // Re-throw validation exceptions to be caught by the calling method (e.g., guardar)
            throw new Exception("Validation failed: " . $e->getMessage());
        }
    }

    // --- GETTERS AND SETTERS ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getPlataforma(): ?string
    {
        return $this->plataforma;
    }

    public function setPlataforma(?string $plataforma): self
    {
        $this->plataforma = $plataforma;
        return $this;
    }

    public function getIdTeam(): ?int
    {
        return $this->id_team;
    }

    public function setIdTeam(?int $id_team): self
    {
        $this->id_team = $id_team;
        return $this;
    }

    public function getNombreEnPlataforma(): ?string
    {
        return $this->nombre_en_plataforma;
    }

    public function setNombreEnPlataforma(?string $nombre_en_plataforma): self
    {
        $this->nombre_en_plataforma = $nombre_en_plataforma;
        return $this;
    }
}
