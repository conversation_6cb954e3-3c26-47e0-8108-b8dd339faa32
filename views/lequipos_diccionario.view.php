<?php
#region region DOCS
/** @var EquipoDiccionario $newequipodiccionario */
/** @var EquipoDiccionario[] $equiposdiccionario */
/** @var array $teams_for_autocomplete */
/** @var array $team_names_map */

use App\classes\EquipoDiccionario;

#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Diccionario de Equipos</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <?php #endregion head ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <!-- BEGIN page-header -->
        <h1 class="page-header">Diccionario de Equipos</h1>

        <hr>
        <!-- END page-header -->

        <?php #region region Main Form Structure (for table and hidden inputs) ?>
        <form action="listado-equipos-diccionario" method="POST" id="mainEquiposDiccionarioForm">
            
            <!-- Button to trigger Add modal -->
            <div class="row mt-3 mb-3">
                <div class="col-md-12">
                    <button type="button" class="btn btn-primary w-100" data-bs-toggle="modal" data-bs-target="#addEquipoDiccionarioModal">
                        <i class="fa fa-plus me-1"></i> Nuevo Registro
                    </button>
                </div>
            </div>

            <?php #region region PANEL equipos diccionario ?>
            <div class="panel panel-inverse mt-3">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <span>Registros del Diccionario:</span>
                    </h4>
                </div>
                <!-- BEGIN panel-body -->
                <div class="p-1 table-nowrap" style="overflow: auto">
                    <?php #region region TABLE equipos diccionario ?>
                    <table class="table table-sm">
                        <thead class="table-dark">
                        <tr>
                            <th class="w-70px">Acciones</th>
                            <th class="text-center">Plataforma</th>
                            <th class="text-center">Equipo</th>
                            <th class="text-center">Nombre en Plataforma</th>
                        </tr>
                        </thead>
                        <tbody class="fs-13px">
                        <?php #region region ARRAY equipos diccionario ?>
                        <?php 
                        // Sort records by team name
                        usort($equiposdiccionario, function($a, $b) use ($team_names_map) {
                            $teamNameA = $team_names_map[$a->getIdTeam()] ?? '';
                            $teamNameB = $team_names_map[$b->getIdTeam()] ?? '';
                            return strcasecmp($teamNameA, $teamNameB);
                        });
                        ?>
                        <?php foreach ($equiposdiccionario as $equipodiccionario): ?>
                            <tr>
                                <td class="align-middle">
                                    <i class="fa fa-trash fa-md cursor-pointer text-danger" data-bs-toggle="modal" data-bs-target="#mdl_delequipodiccionario" data-idequipodiccionario="<?php echo limpiar_datos($equipodiccionario->getId()) ?>"></i>
                                </td>
                                <td class="text-center align-middle">
                                    <?php echo htmlspecialchars($equipodiccionario->getPlataforma()); ?>
                                </td>
                                <td class="align-middle">
                                    <?php echo htmlspecialchars($team_names_map[$equipodiccionario->getIdTeam()] ?? 'Equipo no encontrado'); ?>
                                </td>
                                <td class="align-middle">
                                    <?php echo htmlspecialchars($equipodiccionario->getNombreEnPlataforma()); ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        <?php #endregion array equipos diccionario ?>
                        </tbody>
                    </table>
                    <?php #endregion table equipos diccionario ?>
                </div>
                <!-- END panel-body -->
            </div>
            <?php #endregion panel equipos diccionario ?>
            
            <?php #region region MODAL mdl_delequipodiccionario ?>
            <div class="modal fade" id="mdl_delequipodiccionario">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <input type="hidden" id="mdl_delequipodiccionario_idequipodiccionario" name="mdl_delequipodiccionario_idequipodiccionario">

                        <div class="modal-header">
                            <h4 class="modal-title">Eliminar registro</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <div class="modal-body">
                            <p>¿Está seguro que desea eliminar este registro del diccionario?</p>
                        </div>
                        <div class="modal-footer">
                            <a href="#" class="btn btn-white" data-bs-dismiss="modal">
                                <i class="fa fa-arrow-left fa-lg fa-fw"></i>
                            </a>
                            <button type="submit" form="mainEquiposDiccionarioForm" id="sub_delequipodiccionario" name="sub_delequipodiccionario" class="btn btn-danger">
                                <i class="fa fa-check fa-lg fa-fw"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php #endregion mdl_delequipodiccionario ?>
        </form>
        <?php #endregion Main Form Structure ?>

        <!-- Add EquipoDiccionario Modal -->
        <div class="modal fade" id="addEquipoDiccionarioModal" tabindex="-1" aria-labelledby="addEquipoDiccionarioModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <form action="listado-equipos-diccionario" method="POST" id="addEquipoDiccionarioFormInModal">
                        <div class="modal-header">
                            <h5 class="modal-title" id="addEquipoDiccionarioModalLabel">Agregar Nuevo Registro</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <!-- Plataforma -->
                            <div class="mb-3">
                                <label for="modal_plataforma" class="form-label">Plataforma:</label>
                                <input type="text" name="plataforma" id="modal_plataforma" value="<?php echo htmlspecialchars(@recover_var($newequipodiccionario->getPlataforma() ?? '')); ?>" class="form-control" onclick="this.focus();this.select('')" />
                            </div>
                            <!-- ID Team -->
                            <div class="mb-3">
                                <label for="modal_id_team" class="form-label">Equipo:</label>
                                <input type="text" id="modal_team_search" class="form-control" placeholder="Buscar equipo..." />
                                <input type="hidden" name="id_team" id="modal_id_team" value="<?php echo htmlspecialchars(@recover_var($newequipodiccionario->getIdTeam() ?? '')); ?>" />
                                <small class="form-text text-muted">Escriba para buscar equipos de partidos activos</small>
                            </div>
                            <!-- Nombre en Plataforma -->
                            <div class="mb-3">
                                <label for="modal_nombre_en_plataforma" class="form-label">Nombre en Plataforma:</label>
                                <input type="text" name="nombre_en_plataforma" id="modal_nombre_en_plataforma" value="<?php echo htmlspecialchars(@recover_var($newequipodiccionario->getNombreEnPlataforma() ?? '')); ?>" class="form-control" onclick="this.focus();this.select('')" />
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                            <button type="submit" name="sub_add" class="btn btn-success" id="submitBtn">Agregar Registro</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>

<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function() {
        const addEquipoDiccionarioModal = document.getElementById('addEquipoDiccionarioModal');
        const addForm = document.getElementById('addEquipoDiccionarioFormInModal');

        if (addEquipoDiccionarioModal) {
            addEquipoDiccionarioModal.addEventListener('shown.bs.modal', function () {
                document.getElementById('modal_plataforma').focus(); // Autofocus on the first field
            });
        }

        // Form validation
        if (addForm) {
            addForm.addEventListener('submit', function(e) {
                const plataforma = document.getElementById('modal_plataforma').value.trim();
                const idTeam = document.getElementById('modal_id_team').value.trim();
                const nombreEnPlataforma = document.getElementById('modal_nombre_en_plataforma').value.trim();

                if (!plataforma || !idTeam || !nombreEnPlataforma) {
                    e.preventDefault();
                    swal({
                        text: 'Por favor complete todos los campos requeridos.',
                        icon: 'warning',
                        buttons: {
                            confirm: {
                                text: 'Ok',
                                value: true,
                                visible: true,
                                className: 'btn btn-warning',
                                closeModal: true
                            }
                        }
                    });
                    return false;
                }
            });
        }
    });
</script>

<script type="text/javascript">
    $('#mdl_delequipodiccionario').on('shown.bs.modal', function (event) {
        const button = $(event.relatedTarget);
        const recipient_idequipodiccionario = button.data('idequipodiccionario');

        const mdl_delequipodiccionario_idequipodiccionario = document.getElementById('mdl_delequipodiccionario_idequipodiccionario');

        mdl_delequipodiccionario_idequipodiccionario.value = recipient_idequipodiccionario;
    })
</script>

<!-- BEGIN JS autocomplete for team search -->
<script type="text/javascript">
    $("#modal_team_search").autocomplete({
        source: [
            <?php foreach ($teams_for_autocomplete as $team): ?>
                {
                    label: "<?php echo addslashes($team['team_name']); ?>",
                    value: "<?php echo addslashes($team['team_name']); ?>",
                    id: "<?php echo $team['team_id']; ?>"
                },
            <?php endforeach; ?>
        ],
        select: function(event, ui) {
            // Set the hidden field with the team ID
            document.getElementById('modal_id_team').value = ui.item.id;
            return true;
        },
        change: function(event, ui) {
            // Clear the hidden field if no valid selection
            if (!ui.item) {
                document.getElementById('modal_id_team').value = '';
            }
        }
    }).focus(function() {
        $(this).autocomplete("search", "");
    });
</script>
<!-- END JS autocomplete -->

<?php #endregion js ?>

</body>
</html>
